# Console Brick Breaker Game

A classic arcade-style brick breaker game implemented entirely in Python for console/terminal play.

## Features

- **Console-based graphics** using ASCII characters and ANSI colors
- **Real-time keyboard controls** with cross-platform support (Windows/Linux/macOS)
- **Physics-based ball movement** with realistic bouncing mechanics
- **Collision detection** between ball, paddle, bricks, and walls
- **Multiple colored brick rows** that disappear when hit
- **Score tracking** with points awarded for each brick destroyed
- **Lives system** - lose a life when ball goes off screen
- **Game states** - start screen, playing, game over, and victory
- **Visual feedback** with colored elements and status display

## How to Play

### Objective
Destroy all the colored bricks at the top of the screen by bouncing the ball off your paddle.

### Controls
- **A** or **Left Arrow**: Move paddle left
- **D** or **Right Arrow**: Move paddle right
- **SPACE**: Launch the ball (at start of game or after losing a life)
- **Q** or **ESC**: Quit the game

### Game Mechanics

1. **Starting the Game**: Press SPACE to launch the ball from the paddle
2. **Moving the Paddle**: Use A/D or arrow keys to position your paddle
3. **Ball Physics**: 
   - Ball bounces off walls, paddle, and bricks
   - Hitting different parts of the paddle affects ball angle
   - Ball speed remains constant throughout the game
4. **Scoring**: Earn 10 points for each brick destroyed
5. **Lives**: Start with 3 lives, lose one when ball goes off bottom of screen
6. **Winning**: Destroy all bricks to win the game
7. **Game Over**: Lose all lives to end the game

### Visual Elements

- **Ball**: White circle (●)
- **Paddle**: Cyan bar (█████████)
- **Bricks**: Colored bars in different colors:
  - Red (top row)
  - Yellow (second row)  
  - Green (third row)
  - Blue (fourth row)
  - Magenta (fifth row)
- **Borders**: Box drawing characters (┌─┐│└┘)

## Requirements

- Python 3.6 or higher
- Terminal/console that supports ANSI color codes
- No external dependencies required (uses only standard library)

## Installation and Running

1. **Download the game**:
   ```bash
   # If you have the file locally
   python brick_breaker.py
   ```

2. **Make it executable** (optional, Linux/macOS):
   ```bash
   chmod +x brick_breaker.py
   ./brick_breaker.py
   ```

## Technical Details

### Cross-Platform Compatibility
- **Windows**: Uses `msvcrt` for keyboard input
- **Linux/macOS**: Uses `termios` and `select` for non-blocking input
- **Colors**: ANSI escape codes (supported by most modern terminals)

### Game Architecture
- **Object-oriented design** with separate classes for game components
- **Ball class**: Handles position, velocity, and movement physics
- **Paddle class**: Manages player paddle with boundary checking
- **Brick class**: Individual brick objects with collision detection
- **Game class**: Main controller for game state, rendering, and logic
- **KeyboardInput class**: Cross-platform input handling with threading

### Performance
- **Frame rate**: ~20 FPS (50ms delay between frames)
- **Smooth movement**: Float-based positions with integer rendering
- **Efficient rendering**: Full screen redraw each frame for simplicity

## Troubleshooting

### Common Issues

1. **Colors not showing**: 
   - Ensure your terminal supports ANSI color codes
   - Try a different terminal (Windows Command Prompt, PowerShell, or Windows Terminal)

2. **Keyboard input not working**:
   - Make sure the terminal window has focus
   - Try running with administrator/sudo privileges if needed

3. **Game too fast/slow**:
   - Modify the `time.sleep(0.05)` value in the game loop
   - Smaller values = faster game, larger values = slower game

4. **Screen flickering**:
   - This is normal for console-based games
   - Use a terminal with better refresh rates if available

### Terminal Recommendations
- **Windows**: Windows Terminal, PowerShell, or Git Bash
- **macOS**: Terminal.app or iTerm2
- **Linux**: Any modern terminal emulator

## Code Structure

```
brick_breaker.py
├── Colors class - ANSI color definitions
├── Ball class - Ball physics and movement
├── Paddle class - Player paddle control
├── Brick class - Individual brick objects
├── KeyboardInput class - Cross-platform input handling
├── Game class - Main game controller
└── main() - Entry point and game initialization
```

## Future Enhancements

Potential improvements that could be added:
- Power-ups (multi-ball, larger paddle, etc.)
- Different brick types (requiring multiple hits)
- Sound effects (using system beeps)
- High score persistence
- Multiple levels with different layouts
- Adjustable difficulty settings
- Better graphics with Unicode box-drawing characters

## License

This game is provided as-is for educational and entertainment purposes.

---

**Enjoy playing Console Brick Breaker!** 🎮
