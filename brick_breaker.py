#!/usr/bin/env python3
"""
Console-based Brick Breaker Game
A classic arcade game implemented for terminal/console play.

Controls:
- A/Left Arrow: Move paddle left
- D/Right Arrow: Move paddle right  
- Q: Quit game
- SPACE: Start game/Launch ball

Author: AI Assistant
"""

import os
import sys
import time
import random
import threading
from typing import List, Tuple, Optional

# Cross-platform keyboard input handling
try:
    import msvcrt  # Windows
    WINDOWS = True
except ImportError:
    import tty
    import termios
    import select
    WINDOWS = False

class Colors:
    """ANSI color codes for console output"""
    RESET = '\033[0m'
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'

class Ball:
    """Represents the game ball with position and velocity"""
    
    def __init__(self, x: float, y: float):
        self.x = x
        self.y = y
        self.dx = random.choice([-1, 1]) * 0.5  # Horizontal velocity
        self.dy = -1.0  # Vertical velocity (negative = upward)
        self.char = '●'
        
    def move(self):
        """Update ball position based on velocity"""
        self.x += self.dx
        self.y += self.dy
        
    def bounce_horizontal(self):
        """Reverse horizontal direction"""
        self.dx = -self.dx
        
    def bounce_vertical(self):
        """Reverse vertical direction"""
        self.dy = -self.dy
        
    def get_position(self) -> Tuple[int, int]:
        """Get integer position for rendering"""
        return (int(round(self.x)), int(round(self.y)))

class Paddle:
    """Represents the player's paddle"""
    
    def __init__(self, x: int, y: int, width: int = 8):
        self.x = x
        self.y = y
        self.width = width
        self.char = '█'
        
    def move_left(self, game_width: int):
        """Move paddle left (with boundary checking)"""
        if self.x > 0:
            self.x -= 1
            
    def move_right(self, game_width: int):
        """Move paddle right (with boundary checking)"""
        if self.x + self.width < game_width:
            self.x += 1
            
    def get_center(self) -> int:
        """Get center position of paddle"""
        return self.x + self.width // 2

class Brick:
    """Represents a destructible brick"""
    
    def __init__(self, x: int, y: int, color: str = Colors.RED):
        self.x = x
        self.y = y
        self.width = 4
        self.height = 1
        self.color = color
        self.char = '█'
        self.destroyed = False
        
    def is_hit(self, ball_x: int, ball_y: int) -> bool:
        """Check if ball collides with this brick"""
        if self.destroyed:
            return False
            
        return (self.x <= ball_x < self.x + self.width and 
                self.y <= ball_y < self.y + self.height)

class KeyboardInput:
    """Cross-platform keyboard input handler"""
    
    def __init__(self):
        self.key_pressed = None
        self.running = True
        
        if not WINDOWS:
            # Store original terminal settings
            self.old_settings = termios.tcgetattr(sys.stdin)
            tty.setraw(sys.stdin.fileno())
            
        # Start input thread
        self.input_thread = threading.Thread(target=self._input_loop, daemon=True)
        self.input_thread.start()
        
    def _input_loop(self):
        """Background thread for capturing keyboard input"""
        while self.running:
            if WINDOWS:
                if msvcrt.kbhit():
                    key = msvcrt.getch().decode('utf-8', errors='ignore').lower()
                    self.key_pressed = key
            else:
                if select.select([sys.stdin], [], [], 0.01)[0]:
                    key = sys.stdin.read(1).lower()
                    self.key_pressed = key
            time.sleep(0.01)
            
    def get_key(self) -> Optional[str]:
        """Get the last pressed key and clear it"""
        key = self.key_pressed
        self.key_pressed = None
        return key
        
    def cleanup(self):
        """Restore terminal settings"""
        self.running = False
        if not WINDOWS:
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.old_settings)

class Game:
    """Main game controller"""
    
    def __init__(self, width: int = 80, height: int = 24):
        self.width = width
        self.height = height
        self.score = 0
        self.lives = 3
        self.game_over = False
        self.game_won = False
        self.game_started = False
        
        # Initialize game objects
        self.paddle = Paddle(width // 2 - 4, height - 3, 8)
        self.ball = Ball(width // 2, height - 4)
        self.bricks = self._create_bricks()
        
        # Input handler
        self.keyboard = KeyboardInput()
        
    def _create_bricks(self) -> List[Brick]:
        """Create the initial brick layout"""
        bricks = []
        colors = [Colors.RED, Colors.YELLOW, Colors.GREEN, Colors.BLUE, Colors.MAGENTA]
        
        # Create 5 rows of bricks
        for row in range(5):
            color = colors[row % len(colors)]
            for col in range(0, self.width - 4, 5):  # Space bricks 5 units apart
                if col + 4 <= self.width:  # Ensure brick fits
                    brick = Brick(col, row + 2, color)
                    bricks.append(brick)
                    
        return bricks

    def _clear_screen(self):
        """Clear the console screen"""
        os.system('cls' if os.name == 'nt' else 'clear')

    def _handle_input(self):
        """Process keyboard input"""
        key = self.keyboard.get_key()
        if key:
            if key in ['q', '\x1b']:  # Q or ESC to quit
                self.game_over = True
            elif key in ['a', '\x1b[D']:  # A or Left Arrow
                self.paddle.move_left(self.width)
            elif key in ['d', '\x1b[C']:  # D or Right Arrow
                self.paddle.move_right(self.width)
            elif key == ' ' and not self.game_started:  # Space to start
                self.game_started = True

    def _update_ball(self):
        """Update ball position and handle collisions"""
        if not self.game_started:
            # Keep ball on paddle until game starts
            self.ball.x = self.paddle.get_center()
            self.ball.y = self.paddle.y - 1
            return

        # Move ball
        self.ball.move()
        ball_x, ball_y = self.ball.get_position()

        # Wall collisions
        if ball_x <= 0 or ball_x >= self.width - 1:
            self.ball.bounce_horizontal()

        if ball_y <= 0:
            self.ball.bounce_vertical()

        # Bottom wall (lose life)
        if ball_y >= self.height - 1:
            self.lives -= 1
            if self.lives <= 0:
                self.game_over = True
            else:
                self._reset_ball()

        # Paddle collision
        if (ball_y >= self.paddle.y - 1 and ball_y <= self.paddle.y and
            ball_x >= self.paddle.x and ball_x < self.paddle.x + self.paddle.width):

            self.ball.bounce_vertical()
            # Add some angle based on where ball hits paddle
            hit_pos = (ball_x - self.paddle.x) / self.paddle.width
            self.ball.dx = (hit_pos - 0.5) * 2  # Range from -1 to 1

        # Brick collisions
        for brick in self.bricks:
            if not brick.destroyed and brick.is_hit(ball_x, ball_y):
                brick.destroyed = True
                self.score += 10
                self.ball.bounce_vertical()
                break  # Only hit one brick per frame

        # Check win condition
        if all(brick.destroyed for brick in self.bricks):
            self.game_won = True

    def _reset_ball(self):
        """Reset ball to starting position"""
        self.ball.x = self.paddle.get_center()
        self.ball.y = self.paddle.y - 1
        self.ball.dx = random.choice([-1, 1]) * 0.5
        self.ball.dy = -1.0
        self.game_started = False

    def _render(self):
        """Render the game screen"""
        self._clear_screen()

        # Create screen buffer
        screen = [[' ' for _ in range(self.width)] for _ in range(self.height)]

        # Draw borders
        for x in range(self.width):
            screen[0][x] = '─'
            screen[self.height-1][x] = '─'
        for y in range(self.height):
            screen[y][0] = '│'
            screen[y][self.width-1] = '│'

        # Draw corners
        screen[0][0] = '┌'
        screen[0][self.width-1] = '┐'
        screen[self.height-1][0] = '└'
        screen[self.height-1][self.width-1] = '┘'

        # Draw bricks
        for brick in self.bricks:
            if not brick.destroyed:
                for i in range(brick.width):
                    if brick.x + i < self.width - 1:
                        screen[brick.y][brick.x + i] = brick.char

        # Draw paddle
        for i in range(self.paddle.width):
            if self.paddle.x + i < self.width - 1:
                screen[self.paddle.y][self.paddle.x + i] = self.paddle.char

        # Draw ball
        ball_x, ball_y = self.ball.get_position()
        if 0 <= ball_x < self.width and 0 <= ball_y < self.height:
            screen[ball_y][ball_x] = self.ball.char

        # Render screen with colors
        for y, row in enumerate(screen):
            line = ""
            for x, char in enumerate(row):
                # Color bricks
                colored = False
                for brick in self.bricks:
                    if (not brick.destroyed and brick.y == y and
                        brick.x <= x < brick.x + brick.width):
                        line += brick.color + char + Colors.RESET
                        colored = True
                        break

                if not colored:
                    if char == self.paddle.char:
                        line += Colors.CYAN + char + Colors.RESET
                    elif char == self.ball.char:
                        line += Colors.WHITE + Colors.BOLD + char + Colors.RESET
                    else:
                        line += char
            print(line)

        # Display game info
        print(f"\n{Colors.BOLD}Score: {self.score}  Lives: {self.lives}{Colors.RESET}")

        if not self.game_started and not self.game_over and not self.game_won:
            print(f"{Colors.YELLOW}Press SPACE to launch ball!{Colors.RESET}")
        elif self.game_over:
            print(f"{Colors.RED}{Colors.BOLD}GAME OVER! Press Q to quit.{Colors.RESET}")
        elif self.game_won:
            print(f"{Colors.GREEN}{Colors.BOLD}YOU WIN! Final Score: {self.score}{Colors.RESET}")

        print(f"{Colors.CYAN}Controls: A/D or Arrow Keys to move, Q to quit{Colors.RESET}")

    def run(self):
        """Main game loop"""
        try:
            while not self.game_over and not self.game_won:
                self._handle_input()
                self._update_ball()
                self._render()
                time.sleep(0.05)  # Control game speed

            # Keep showing final screen until quit
            while True:
                self._handle_input()
                if self.game_over or self.game_won:
                    break
                time.sleep(0.1)

        except KeyboardInterrupt:
            pass
        finally:
            self.keyboard.cleanup()
            self._clear_screen()
            print("Thanks for playing Brick Breaker!")

def main():
    """Entry point for the game"""
    print(f"{Colors.BOLD}{Colors.CYAN}Welcome to Console Brick Breaker!{Colors.RESET}")
    print("Loading game...")
    time.sleep(1)

    game = Game()
    game.run()

if __name__ == "__main__":
    main()
